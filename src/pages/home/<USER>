<template>
  <div >
    <!--    首页轮播图结构-->
    <Carousel></Carousel>
<!--    首页搜索医院表单-->
    <search />
<!--    底部展示医院的结构-->
    <el-row gutter="20">
      <el-col :span="20">
<!--        等级子组件-->
        <Level></Level>
<!--        地区子组件-->
        <Region></Region>
<!--        展示医院结构-->
        <div class="hospiatlCard">
          <Hospital class="item" v-for="item in 10" :key="item"></Hospital>
        </div>
        <!--          分页器-->
        <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 40]"
            :background="true"
            layout="->, prev, pager, next, jumper, total, sizes"
            :total="20"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </el-col>
      <el-col :span="4">456</el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
// 引入轮播图组件
import Carousel from './carousel/index.vue'
import Search from './search/index.vue'
import Level from './level/index.vue'
import Region from './region/index.vue'
import Hospital from './card/index.vue'

// 分页器数据
let pageNo = ref<number>(1);
let pageSize = ref<number>(10);
</script>

<style scoped>
.hospiatlCard{
  display: flex;
  flex-wrap:wrap;
  justify-content:space-between;
  .item {
    width: 48%;
    margin: 10px 0px;
  }

}
</style>
