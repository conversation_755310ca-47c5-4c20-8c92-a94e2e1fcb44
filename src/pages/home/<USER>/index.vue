<template>
  <div class="search">
    <el-autocomplete
        :fetch-suggestions="querySearch"
        @select="goDetail"
        :trigger-on-focus="false"
        v-model="hosname"
        clearable
        placeholder="请输入医院名称"></el-autocomplete>
    <el-button type="primary" size="default" :icon="Search">搜索</el-button>
  </div>
</template>

<script setup lang="ts">
// 引入 element-plus 图标
import { Search } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.search{
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin : 10px 0;
  button{
    margin-left: 6px;
  }
  /*深度选择器*/
  ::v-deep(.el-input__wrapper){
    width: 600px;
  }

}
</style>
