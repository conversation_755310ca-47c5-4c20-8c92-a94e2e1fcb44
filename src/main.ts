// vue3框架提供的 create 方法
import { createApp } from 'vue'
// 清除默认样式
import '@/style/reset.scss'
// 引入全局组价
import HospitalTop from '@/components/hospital_top/index.vue'
import HospitalBottom from '@/components/hospital_bottom/index.vue'

// 引入 vue-router
import router from "@/router";

// 引入 element-plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 引入根组件 app
import App from '@/App.vue'
// 讲应用实例挂载到挂载点上

const app = createApp(App)
app.component('HospitalTop',HospitalTop)
app.component('HospitalBottom',HospitalBottom)
// 安装 vue-router
app.use(router)
// 安装 element-plus
app.use(ElementPlus,{
    locale:zhCn
})

app.mount('#app')
